local Translations = {
    error = {
        canceled = 'İptal Edildi',
        bled_out = '<PERSON><PERSON><PERSON> var',
        impossible = '<PERSON><PERSON><PERSON> geçersiz',
        no_player = 'Yakında oyuncu yok',
        no_firstaid = 'Sağlık Çantasına ihtiyacınız var',
        no_bandage = 'Bandaja ihtiyacınız var',
        beds_taken = 'Yataklar dolu',
        possessions_taken = 'Tüm eşyaların alındı',
        not_enough_money = 'Yet<PERSON><PERSON> paran yok',
        cant_help = 'Bu kişiye yardım edemezsin',
        not_ems = 'EMS değilsiniz veya mesaide değilsiniz',
        not_online = 'Oyuncu aktif değil'
    },
    success = {
        revived = 'Yaralıyı iyileştirdin',
        healthy_player = '<PERSON>ta sağlıklı',
        helped_player = 'Kişiye yardım ettin',
        wounds_healed = 'Ya<PERSON>arın iyile<PERSON>',
        being_helped = 'Sana yardım ediliyor'
    },
    info = {
        civ_died = 'Civilian Died',
        civ_down = 'Civilian Down',
        civ_call = 'Civilian Call',
        self_death = 'Themselves or an NPC',
        wep_unknown = 'Unknown',
        respawn_txt = 'RESPAWN IN: ~r~%{deathtime}~s~ SECONDS',
        respawn_revive = 'HOLD [~r~E~s~] FOR %{holdtime} SECONDS TO RESPAWN FOR $~r~%{cost}~s~',
        bleed_out = 'YOU WILL BLEED OUT IN: ~r~%{time}~s~ SECONDS',
        bleed_out_help = 'YOU WILL BLEED OUT IN: ~r~%{time}~s~ SECONDS, YOU CAN BE HELPED',
        request_help = 'PRESS [~r~G~s~] TO REQUEST HELP',
        help_requested = 'EMS PERSONNEL HAVE BEEN NOTIFIED',
        amb_plate = 'AMBU', -- Should only be 4 characters long due to the last 4 being a random 4 digits
        heli_plate = 'LIFE',  -- Should only be 4 characters long due to the last 4 being a random 4 digits
        status = 'Status Check',
        is_staus = 'Is %{status}',
        healthy = 'Tamamen sağlıklısın!',
        safe = 'Hospital Safe',
        pb_hospital = 'Hastane',
        pain_message = 'Your %{limb} feels %{severity}',
        many_places = 'You have pain in many places...',
        bleed_alert = 'You are %{bleedstate}',
        ems_alert = 'EMS Alert - %{text}',
        mr = 'Mr.',
        mrs = 'Mrs.',
        dr_needed = 'A doctor is needed at Pillbox Hospital',
        ems_report = 'EMS Report',
        message_sent = 'Message to be sent',
        check_health = 'Check a Players Health',
        heal_player = 'Heal a Player',
        revive_player = 'Revive a Player',
        revive_player_a = 'Bir Oyuncuyu veya Kendinizi Canlandırın (Yalnızca adminler)',
        player_id = 'Player ID (may be empty)',
        pain_level = 'Bir Oyuncunun veya Kendinizin ağrılarını azaltır (Yalnızca adminler)',
        kill = 'Bir Oyuncuyu veya Kendinizi Öldürün (Yalnızca adminler)',
        heal_player_a = 'Bir Oyuncuyu veya Kendinizi İyileştirin (Yalnızca adminler)',
    },
    mail = {
        sender = 'Hastane',
        subject = 'Hastane Maliyetleri',
        message = 'Sayın %{gender} %{lastname}, <br /><br />Bu vesile ile son hastane ziyaretinizin masraflarını içeren bir e-posta aldınız.<br />Nihai masraflar şu şekilde oldu: <strong>$%{costs} </strong><br /><br />Size acil şifalar diliyoruz!'
    },
    states = {
        irritated = 'irritated',
        quite_painful = 'quite painful',
        painful = 'painful',
        really_painful = 'really painful',
        little_bleed = 'bleeding a little bit...',
        bleed = 'bleeding...',
        lot_bleed = 'bleeding a lot...',
        big_bleed = 'bleeding very much...',
    },
    menu = {
        amb_vehicles = 'EMS Araçlar',
        status = 'Sağlık Durumu',
        close = '⬅ Menüyü Kapat',
    },
    text = {
        pstash_button = '[E] - Personal stash',
        pstash = 'Personal stash',
        onduty_button = '[E] - Go On Duty',
        offduty_button = '[E] - Go Off Duty',
        duty = 'On/Off Duty',
        armory_button = '[E] - Armory',
        armory = 'Armory',
        veh_button = '[E] - Araç Çıkar / Garaja Bırak',
        heli_button = '[E] - Helikopter Çıkar / Helikopter Bırak',
        elevator_roof = '[E] - Take the elevator to the roof',
        elevator_main = '[E] - Take the elevator down',
        bed_out = '[E] - Yataktan Kalk',
        call_doc = '[E] - Call doctor',
        call = 'Call',
        check_in = '[E] Tedavi Ol',
        check = 'Tedavi Ol',
        lie_bed = '[E] - To lie in bed'
    },
    body = {
        head = 'Head',
        neck = 'Neck',
        spine = 'Spine',
        upper_body = 'Upper Body',
        lower_body = 'Lower Body',
        left_arm = 'Left Arm',
        left_hand = 'Left Hand',
        left_fingers = 'Left Fingers',
        left_leg = 'Left Leg',
        left_foot = 'Left Foot',
        right_arm = 'Right Arm',
        right_hand = 'Right Hand',
        right_fingers = 'Right Fingers',
        right_leg = 'Right Leg',
        right_foot = 'Right Foot',
    },
    progress = {
        ifaks = 'Ifak Kullanılıyor',
        bandage = 'Bandaj Kullanılıyor',
        painkillers = 'Ağrı Kesici Kullanılıyor',
        revive = 'İlk Yardım Yapılıyor',
        healing = 'Hasta Kontrol Ediliyor',
        checking_in = 'Tedavi olunuyor',
    },
    logs = {
        death_log_title = "%{playername} (%{playerid}) öldü",
        death_log_message = "%{killername}, %{playername}'ı bir **%{weaponlabel}** (%{weaponname}) ile öldürdü",
    }
}

Lang = Lang or Locale:new({
    phrases = Translations,
    warnOnMissing = true
})
